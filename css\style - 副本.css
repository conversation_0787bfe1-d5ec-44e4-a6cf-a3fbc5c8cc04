/* 默认字体 - LXG<PERSON> WenKai */
@font-face {
  font-family: "LXGW WenKai";
  src: url("../fonts/LXGWWenKai-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "LXG<PERSON> WenKai";
  src: url("../fonts/LXGWWenKai-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}

/* 仓耳字体系列 */
@font-face {
  font-family: "仓耳华新体";
  src: url("../fonts/仓耳华新体.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "仓耳今楷03-W03";
  src: url("../fonts/仓耳今楷03-W03.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "仓耳今楷03-W04";
  src: url("../fonts/仓耳今楷03-W04.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "仓耳今楷03-W05";
  src: url("../fonts/仓耳今楷03-W05.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "仓耳今楷04-W03";
  src: url("../fonts/仓耳今楷04-W03.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "仓耳今楷04-W04";
  src: url("../fonts/仓耳今楷04-W04.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "仓耳今楷04-W05";
  src: url("../fonts/仓耳今楷04-W05.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "仓耳今楷05-W04";
  src: url("../fonts/仓耳今楷05-W04.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "仓耳聚珍长仿";
  src: url("../fonts/仓耳聚珍长仿.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "仓耳宋楷";
  src: url("../fonts/仓耳宋楷.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "屏显臻宋";
  src: url("../fonts/屏显臻宋.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

html {
  font-size: 18px;
  scroll-behavior: smooth;
  font-family: "LXGW WenKai", sans-serif;
  scrollbar-width: thin; /* Firefox */
}

body {
  flex-direction: column;
  height: 100vh;
  margin: 0 10px;
  position: relative;
}

/* 页眉样式 */
.page-header {
  font-size: 1.3rem;
  font-weight: bolder;
  color: red;
  text-align: center;
  padding: 5px;
  border-bottom: 1px solid #006400;
}

.header-title {
  font-size: 1.6rem;
  font-weight: bold;
  color: #006400;
  text-align: center;
  margin: 0;
}

/* 面包屑导航样式 */
.breadcrumb-nav {
  padding: 8px;
  margin: 15px 0 10px 0;
  background-color: #ddeadc;
  border-radius: 8px;
  font-size: 0.9rem;
  display: none; /* 默认隐藏，只在需要时显示 */
}

.breadcrumb-nav.show {
  display: block;
}

.breadcrumb-nav a {
  color: #006400;
  text-decoration: none;
}

.breadcrumb-nav a:hover {
  text-decoration: underline;
}

.breadcrumb-nav .current {
  color: #333;
  font-weight: normal;
}

.breadcrumb-nav .separator {
  color: #999;
  margin: 0 4px;
}

/* 移动端面包屑优化 */
@media (max-width: 768px) {
  .breadcrumb-nav {
    padding: 6px 10px;
    font-size: 0.8rem;
    margin: 12px 0 8px 0;
    border-radius: 6px;
    /* 支持水平滚动 */
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .breadcrumb-nav::-webkit-scrollbar {
    display: none;
  }

  .breadcrumb-nav a {
    font-size: 0.8rem;
    display: inline-block;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: top;
  }

  .breadcrumb-nav .current {
    font-size: 0.8rem;
    display: inline-block;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: top;
  }

  .breadcrumb-nav .separator {
    margin: 0 2px;
    white-space: nowrap;
    display: inline-block;
  }
}

p {
  margin: 10px 0;
  font-size: 1.2rem;
  text-align: justify;
  border-radius: 5px;
}

p > strong {
  color: #006400;
}

.header,
.footer {
  font-size: 1.2rem;
  text-align: center;
  padding-bottom: 10px;
}

.header a,
.footer a {
  margin-right: 20px;
  outline: none;
  text-decoration: none;
}

/* 添加导航栏样式 */
.navigation {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10px 0;
  padding: 5px 0;
}

.navigation a {
  margin: 0 10px;
  padding: 5px 10px;
  text-decoration: none;
  color: #006400;
  border: 1px solid #006400;
  border-radius: 5px;
  background-color: rgba(255, 255, 255, 0.7);
}

.navigation a:hover {
  background-color: #006400;
  color: white;
}

.markdown-content {
  padding-bottom: 46px;
}

#content {
  flex: 1;
  border-radius: 5px;
  margin: 10px 0;
  padding: 0 10px;
}

div > p {
  text-indent: 2em;
}

div.title {
  text-align: center;
  margin: 10px 0;
  padding: 5px;
  font-size: 1.4rem;
  font-weight: bolder;
  color: #006400;
  border-radius: 5px;
}

/* 返回顶部按钮样式 */
.totop {
  font-size: 16px;
  text-align: center;
  position: fixed;
  width: 40px;
  height: 40px;
  bottom: 20px;
  right: 5px;
  background-color: rgba(66, 100, 66, 0.5);
  color: white;
  border: none;
  padding: 5px 5px;
  cursor: pointer;
  border-radius: 50%;
  box-shadow: 2px 2px 5px rgba(45, 58, 49, 0.3);
  -webkit-tap-highlight-color: transparent;
}
.totop:hover {
  background-color: rgba(0, 100, 0, 0.5);
  transform: scale(1.1);
}

/* 搜索触发按钮样式 */
.search-trigger {
  font-size: 18px;
  text-align: center;
  position: fixed;
  width: 40px;
  height: 40px;
  bottom: 200px;
  right: 5px;
  background-color: rgba(66, 100, 66, 0.5);
  color: white;
  border: none;
  padding: 5px 5px;
  cursor: pointer;
  border-radius: 50%;
  box-shadow: 2px 2px 5px rgba(45, 58, 49, 0.3);
  -webkit-tap-highlight-color: transparent;
  z-index: 997;
}

.search-trigger:hover {
  background-color: rgba(0, 100, 0, 0.5);
}

/* 索引管理按钮样式 */
.index-manage {
  font-size: 16px;
  text-align: center;
  position: fixed;
  width: 40px;
  height: 40px;
  bottom: 80px;
  right: 5px;
  background-color: rgba(66, 100, 66, 0.5);
  color: white;
  border: none;
  cursor: pointer;
  border-radius: 50%;
  box-shadow: 2px 2px 5px rgba(45, 58, 49, 0.3);
  -webkit-tap-highlight-color: transparent;
  z-index: 996;
  transition: all 0.3s ease;
}

.index-manage:hover {
  background-color: rgba(0, 100, 0, 0.5);
  transform: scale(1.1);
}

/* 悬浮搜索框容器 */
.floating-search-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  pointer-events: none; /* 默认不接收事件 */
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.floating-search-container.active {
  opacity: 1;
  visibility: visible;
  pointer-events: auto; /* 显示时接收事件 */
}

/* 悬浮搜索框 */
.floating-search-box {
  width: 90%;
  max-width: 500px;
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  touch-action: auto;
}

/* 搜索结果选项卡样式 */
.results-tabs {
  display: flex;
  margin-bottom: 0;
  border-bottom: 2px solid #f0f0f0;
  border-radius: 6px 6px 0 0;
  overflow: hidden;
}

.results-tabs .tab-item {
  flex: 1;
  padding: 12px 15px;
  text-align: center;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  font-size: 14px;
  color: white;
  user-select: none;
  background: #006400;
  border-right: 1px solid #e0e0e0;
}

.results-tabs .tab-item:last-child {
  border-right: none;
}

.results-tabs .tab-item:hover {
  background-color: #005000;
  color: white;
}

.results-tabs .tab-item.active {
  color: #006400;
  border-bottom-color: #006400;
  background-color: white;
  font-weight: bold;
  box-shadow: 0 -2px 4px rgba(0, 100, 0, 0.1);
}

/* 搜索结果头部容器 - 使用flex纵向布局 */
.results-header {
  display: flex;
  flex-direction: column;
  background-color: #006400;
  color: white;
  border-radius: 8px 8px 0 0;
}

/* 结果信息行样式 - 计数和控制按钮在同一行 */
.results-info-line {
  display: flex;
  padding: 10px 15px;
  margin-top: 5px;
}

.results-info-line .results-count {
  font-size: 18px;
  margin: 0;
}

.results-info-line .results-controls {
  display: flex;
  gap: 8px;
  align-items: center;
  margin: 0;
}

.floating-search-box input {
  width: 100%;
  padding: 10px;
  border: 1px solid #006400;
  border-radius: 5px;
  font-size: 16px;
  font-family: "LXGW WenKai", sans-serif;
  margin-bottom: 15px;
  outline: none;
}

.floating-search-buttons {
  display: flex;
  gap: 10px;
}

.floating-search-buttons button {
  padding: 10px 15px;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-family: "LXGW WenKai", sans-serif;
  white-space: nowrap;
}

#search-button {
  background-color: #006400;
  color: white;
}

#search-button:hover {
  background-color: #004d00;
}

#close-search-button {
  background-color: #f0f0f0;
  color: #333;
}

#close-search-button:hover {
  background-color: #e0e0e0;
}

/* 搜索分类选择器样式 */
.search-category-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 10px 0;
  font-size: 14px;
}

.search-category-selector label {
  color: #333;
  font-weight: 500;
  white-space: nowrap;
}

.search-category-selector select {
  flex: 1;
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  font-size: 14px;
  color: #333;
  cursor: pointer;
}

.search-category-selector select:focus {
  outline: none;
  border-color: #006400;
  box-shadow: 0 0 0 2px rgba(0, 100, 0, 0.1);
}

.search-category-selector select option {
  padding: 5px;
}

ul {
  font-size: 1.4rem;
  font-weight: bolder;
  text-indent: -1.8em;
  padding: 0 10px 0 50px;
  margin: 5px 0;
}

ul li {
  text-align: justify;
  list-style: none;
}

nav ul {
  list-style-type: none;
  line-height: 1.5;
  justify-content: space-around;
}
li a {
  text-decoration: none;
}
nav ul li a {
  text-decoration: none;
  color: #006400;
  outline: none;
}

ul.toc-list {
  font-size: 1.2rem;
  color: #006400;
  text-align: center;
  padding: 0;
  margin: 0 10px;
  font-weight: normal;
  text-indent: 0em;
}

#books-list {
  font-size: 1.4rem;
  font-weight: bolder;
  text-indent: -1.8em;
  padding: 0 10px 0 50px;
  margin: 5px 0;
}

div.toc {
  font-size: 1.3rem;
  text-align: center;
  color: red;
}

nav {
  font-size: 1.3rem;
  border-top: 0;
}

.toc {
  border: 1px solid #006400;
}

.toc-item a {
  text-decoration: none;
  color: #006400;
}

.toc-item a:hover {
  color: red;
}

.toc-item-active {
  color: red;
}

.toc-title {
  font-size: 1.3rem;
  font-weight: bolder;
  color: red;
  text-align: center;
  padding: 5px;
  border-bottom: 1px solid #006400;
}
.booktoc-list {
  list-style-type: none;
  padding-left: 20px; /* 每一级目录向左缩进 20px */
  margin: 0; /* 重置 margin */
}
u > img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 5px auto;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.3);
}

h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a {
  margin: 10px 0;
  padding: 5px;
  text-decoration: none;
  color: red;
  font-weight: bolder;
}
h1 a:hover,
h2 a:hover,
h3 a:hover,
h4 a:hover,
h5 a:hover,
h6 a:hover {
  color: #006400;
}

h1 a:active,
h2 a:active,
h3 a:active,
h4 a:active,
h5 a:active,
h6 a:active {
  color: red;
}

h1 {
  font-size: 1.4rem;
  text-align: center;
  color: #006400;
  font-weight: bold;
  margin: 5px 0;
}

h2,
h3,
h4,
h5,
h6 {
  font-size: 1.3rem;
  margin: 5px 0;
  text-decoration: none;
  font-weight: bold;
  font-family: "LXGW WenKai", sans-serif;
}

mark {
  background-color: rgba(158, 222, 158, 0.3);
}

/* 添加搜索框样式 */
.search-container {
  display: flex;
  justify-content: center;
  margin: 10px 0;
  padding: 5px;
}

#search-input {
  width: 60%;
  padding: 8px;
  border: 1px solid #006400;
  border-radius: 5px;
  font-size: 1rem;
  font-family: "LXGW WenKai", sans-serif;
}

#search-button {
  padding: 8px 15px;
  background-color: #006400;
  color: white;
  border: 1px solid #006400;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  font-family: "LXGW WenKai", sans-serif;
}

#search-button:hover {
  background-color: #004d00;
}

.search-results {
  margin: 0 auto;
  max-width: 90%;
  min-height: 500px; /* 增加最小高度 */
  max-height: 80vh; /* 增加最大高度 */
  border: 1px solid #006400;
  border-radius: 5px;
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止整个容器滚动 */
}

/* 搜索中状态的特殊样式 */
.search-results.loading-state {
  height: 150px; /* 搜索中时使用固定的小高度 */
  min-height: 150px;
  max-height: 150px;
}

/* 固定的头部区域 */
.search-results-header {
  flex-shrink: 0; /* 不缩小 */
  padding: 10px 15px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #006400;
  color: white;
  border-radius: 5px 5px 0 0;
}

/* 可滚动的内容区域 */
.results-content {
  flex: 1 1 0; /* 占据剩余空间，可以缩小 */
  overflow-y: auto !important;
  overflow-x: hidden !important;
  padding: 0 15px;
  -webkit-overflow-scrolling: touch;
  /* 强制设置高度和滚动 */
  min-height: 0 !important; /* 重要：让flex子项可以缩小 */
  max-height: calc(80vh - 140px) !important; /* 减去头部和底部的高度 */
}

/* 固定的底部分页区域 */
.pagination-container {
  flex-shrink: 0; /* 不缩小 */
  border-top: 1px solid #e0e0e0;
  background-color: white;
  border-radius: 0 0 5px 5px;
}

.search-results.floating {
  width: 90%;
  max-width: 700px;
  margin: 0;
  border-radius: 8px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.25);
  padding: 0;
  /* 保持flex布局，不要覆盖为block */
}

.results-count {
  font-weight: bold;
  font-size: 1.1rem;
  margin: 0;
  flex: 1;
}

.powered-by {
  font-size: 0.8rem;
  font-weight: normal;
  color: rgba(255, 255, 255, 0.8);
  margin-left: 8px;
}

.results-controls {
  display: flex;
  gap: 5px;
  align-items: center;
  margin: 0;
}

.expand-all-btn,
.collapse-all-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 0.9rem;
  cursor: pointer;
  white-space: nowrap;
}

.expand-all-btn:hover,
.collapse-all-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.close-results-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0 5px;
  margin: 0;
  line-height: 1;
}

.close-results-btn:hover {
  color: #f0f0f0;
}

.search-result-item {
  margin: 0;
  padding: 10px 0;
  border-bottom: 1px solid #ccc;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background-color: rgba(0, 100, 0, 0.02);
}

.search-result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-result-title {
  font-weight: bold;
  color: #006400;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  width: 100%;
}

.search-result-title:hover {
  background-color: rgba(0, 100, 0, 0.1);
}

.toggle-icon {
  margin-left: auto;
  font-size: 12px;
  transition: transform 0.2s ease;
}

.toggle-icon.expanded {
  transform: rotate(0deg);
}

.toggle-icon.collapsed {
  transform: rotate(-90deg);
}

.search-result-contexts {
  margin-left: 10px;
  border-left: 3px solid #eee;
  padding-left: 10px;
  padding-top: 5px;
  padding-bottom: 5px;
  transition: all 0.3s ease;
}

/* 匹配项样式 */
.context-item {
  margin: 5px 0;
  padding: 5px;
  border-bottom: 1px dashed #eee;
  display: flex;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.context-item:hover {
  background-color: rgba(0, 100, 0, 0.1);
}

.context-item:last-child {
  border-bottom: none;
}

.context-number {
  flex: 0 0 25px;
  font-weight: bold;
  color: #006400;
}

.context-text {
  flex: 1;
}

.title-match {
  font-weight: bold;
  color: #006400;
  background-color: rgba(0, 100, 0, 0.05);
  border-radius: 3px;
}

.content-match {
  color: #333;
}

.footnote-match {
  color: #ff6600;
  background-color: rgba(255, 102, 0, 0.05);
  border-radius: 3px;
  border-left: 3px solid #ff6600;
  padding-left: 8px;
}

.footnote-match .context-number {
  color: #ff6600;
}

.footnote-match::before {
  content: "📝 ";
  font-size: 0.8rem;
  margin-right: 4px;
}

.more-matches {
  font-style: italic;
  color: #666;
  text-align: right;
  margin-top: 5px;
  font-size: 0.9rem;
}

.match-count {
  font-size: 0.9rem;
  color: #666;
  font-weight: normal;
  margin-left: 5px;
  margin-right: 5px;
}

/* 相关性评分样式 */
.relevance-score {
  font-size: 0.8rem;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
  margin-right: 5px;
}

.relevance-score.high-relevance {
  background-color: #4caf50;
  color: white;
}

.relevance-score.medium-relevance {
  background-color: #ff9800;
  color: white;
}

.relevance-score.low-relevance {
  background-color: #9e9e9e;
  color: white;
}

/* 章节内容中的搜索信息样式 */
.search-info {
  margin: 10px 0;
  padding: 10px;
  background-color: rgba(0, 100, 0, 0.1);
  border: 1px solid #006400;
  border-radius: 5px;
  font-size: 1rem;
}

/* 悬浮搜索导航面板样式 */
.search-info.floating {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  margin: 0;
  max-width: 350px;
  transition: all 0.3s ease;
}

/* 在移动设备上调整悬浮面板位置 */
@media (max-width: 768px) {
  .search-info.floating {
    bottom: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
}

.search-info-text {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.highlight-count {
  font-weight: bold;
  color: #006400;
  margin: 0 5px;
}

.highlight-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: flex-end;
}

.nav-highlight-btn,
.clear-highlight-btn {
  padding: 3px 8px;
  background-color: #006400;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.9rem;
}

.nav-highlight-btn:hover,
.clear-highlight-btn:hover {
  background-color: #004d00;
}

.current-highlight,
.total-highlights {
  font-weight: bold;
  color: #006400;
}

/* 使章节内容中的高亮更明显 */
#content .search-highlight {
  background-color: #ffff00;
  color: #000;
  font-weight: bold;
  padding: 0 2px;
  border-radius: 3px;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
}

/* 当前激活的高亮样式 */
#content .search-highlight.active-highlight {
  background-color: #ff9900;
  box-shadow: 0 0 5px rgba(255, 153, 0, 0.5);
  position: relative;
  z-index: 2;
}

/* 搜索结果区域样式增强 */
.floating-results-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 998;
  opacity: 0;
  visibility: hidden;
  pointer-events: none; /* 默认不接收事件 */
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.floating-results-container.active {
  opacity: 1;
  visibility: visible;
  pointer-events: auto; /* 显示时接收事件 */
}

.search-results.floating {
  width: 90%;
  max-width: 700px;
  margin: 0;
  border-radius: 8px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.25);
  padding: 0;
  /* 保持flex布局，不要覆盖为block */
}

/* 搜索高亮样式 */
.search-highlight {
  background-color: #ffff00;
  color: #000;
  font-weight: bold;
  padding: 0 1px;
  border-radius: 2px;
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.2);
}

html.no-scroll body {
  padding-right: 15px; /* 模拟滚动条宽度，防止布局偏移 */
}

/* 弹窗显示时禁止底部内容滚动 */
html.modal-open,
html.modal-open body {
  overflow: hidden;
  touch-action: none;
}

/* 但是允许搜索结果内容区域滚动 */
html.modal-open .results-content,
html.modal-open .search-results .results-content,
.search-results .results-content {
  overflow-y: auto !important;
  overflow-x: hidden !important;
  touch-action: auto !important;
  -webkit-overflow-scrolling: touch !important;
}

/* 允许脚注弹窗内容区域滚动 */
html.modal-open .footnote-popup-content {
  overflow-y: auto !important;
  overflow-x: hidden !important;
  touch-action: auto !important;
  -webkit-overflow-scrolling: touch !important;
}

/* 确保脚注弹窗整体也允许滚动事件 */
html.modal-open .footnote-popup {
  touch-action: auto !important;
}

/* 允许设置面板内容滚动 */
html.modal-open .settings-content {
  overflow-y: auto !important;
  touch-action: auto !important;
  -webkit-overflow-scrolling: touch !important;
}

/* 允许索引管理面板内容滚动 */
html.modal-open #index-management {
  overflow-y: auto !important;
  touch-action: auto !important;
  -webkit-overflow-scrolling: touch !important;
}

/* 确保弹窗内容可滚动 */
.floating-search-box {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  touch-action: auto;
}

/* 弹窗显示时的覆盖层样式 */
.floating-search-container.active:before,
.floating-results-container.active:before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

/* 脚注引用样式 */
.footnote-ref {
  text-decoration: none;
  color: #006400;
  font-weight: bold;
  font-size: 0.8em;
  vertical-align: super;
}

.footnote-ref:hover {
  text-decoration: underline;
  color: #004d00;
}

/* 脚注弹窗背景遮罩 */
.footnote-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.footnote-popup-overlay.show {
  opacity: 1;
}

/* 脚注弹窗样式 */
.footnote-popup {
  position: fixed;
  left: 50%;
  top: 50%;
  background: white;
  border: 2px solid #006400;
  border-radius: 12px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.4);
  max-width: 500px;
  min-width: 350px;
  z-index: 10000;
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.9);
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.footnote-popup.show {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.footnote-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  border-radius: 6px 6px 0 0;
}

.footnote-popup-title {
  font-weight: bold;
  color: #006400;
  font-size: 0.9rem;
}

.footnote-popup-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.footnote-popup-close:hover {
  background: #e9ecef;
  color: #333;
}

.footnote-popup-content {
  padding: 15px;
  line-height: 1.5;
  color: #333;
  max-height: 300px;
  overflow-y: auto;
}

.footnote-popup-content p {
  margin: 0 0 10px 0;
  line-height: 1.6;
}

.footnote-popup-content p:last-child {
  margin-bottom: 0;
}

.footnote-popup-content strong {
  font-weight: bold;
}

.footnote-popup-content em {
  font-style: italic;
}

.footnote-popup-content a {
  color: #006400;
  text-decoration: none;
}

.footnote-popup-content a:hover {
  text-decoration: underline;
}

.footnote-popup-footer {
  padding: 10px 15px;
  border-top: 1px solid #dee2e6;
  background: #f8f9fa;
  border-radius: 0 0 6px 6px;
  text-align: center;
}

.footnote-popup-close-btn {
  background: #006400;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background 0.2s ease;
}

.footnote-popup-close-btn:hover {
  background: #004d00;
}

/* 移动端脚注优化 */
@media (max-width: 768px) {
  .footnotes {
    font-size: 0.8em;
    margin-top: 1.5em;
  }

  .footnote-ref {
    font-size: 0.75em;
  }

  .footnotes-list {
    padding-left: 1.2em;
  }

  .footnote-popup {
    max-width: 95vw;
    min-width: 300px;
    font-size: 0.85rem;
    max-height: 80vh;
  }

  .footnote-popup-content {
    max-height: 150px;
    padding: 12px;
  }

  .footnote-popup-header,
  .footnote-popup-footer {
    padding: 8px 12px;
  }
}

/* 加载状态样式 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 1.1rem;
  color: #666;
  background-color: rgba(255, 255, 255, 0.9);
}

/* 加载状态样式 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%; /* 填满整个容器 */
  font-size: 1.1rem;
  color: #666;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
}

/* 搜索错误状态样式 */
.search-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%; /* 填满整个容器 */
  font-size: 1.1rem;
  color: #dc3545;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  text-align: center;
}

/* 无搜索结果状态样式 */
.no-results {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px; /* 固定高度 */
  font-size: 1.1rem;
  color: #6c757d;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  text-align: center;
  margin: 20px;
}

/* 分页样式 */
.pagination-container {
  margin: 15px 0;
  padding: 0 20px;
}

.pagination {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
}

.pagination-info {
  font-size: 0.9rem;
  color: #666;
  text-align: center;
}

.pagination-buttons {
  display: flex;
  gap: 3px;
  align-items: center;
  flex-wrap: nowrap; /* 防止换行 */
  justify-content: center;
  overflow-x: auto; /* 如果太宽，允许水平滚动 */
}

.pagination-btn {
  padding: 6px 10px;
  border: 1px solid #ddd;
  background: white;
  color: #333;
  cursor: pointer;
  border-radius: 4px;
  font-size: 0.85rem;
  transition: all 0.2s ease;
  min-width: 32px;
  text-align: center;
  white-space: nowrap; /* 防止文字换行 */
}

.pagination-btn:hover {
  background: #f5f5f5;
  border-color: #006400;
}

.pagination-btn.active {
  background: #006400;
  color: white;
  border-color: #006400;
}

.pagination-btn:disabled {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
  border-color: #eee;
}

.pagination-ellipsis {
  padding: 8px 4px;
  color: #999;
  font-size: 0.9rem;
}

/* 响应式分页 */
@media (max-width: 768px) {
  .pagination-buttons {
    gap: 2px;
    overflow-x: auto; /* 允许水平滚动 */
    -webkit-overflow-scrolling: touch;
  }

  .pagination-btn {
    padding: 5px 6px;
    font-size: 0.75rem;
    min-width: 28px;
    flex-shrink: 0; /* 防止按钮被压缩 */
  }

  .pagination-ellipsis {
    padding: 5px 2px;
    font-size: 0.75rem;
  }
}

/* ===== 设置面板样式 ===== */
:root {
  --content-font-size: 16px;
  --content-font-family: "LXGW WenKai", sans-serif;
  --content-line-height: 1.6;
  --content-max-width: 800px;
}

/* 应用设置变量到内容区域 */
#content {
  font-size: var(--content-font-size);
  font-family: var(--content-font-family);
  line-height: var(--content-line-height);
  max-width: var(--content-max-width);
  margin: 10px auto;
}

#content p {
  font-size: var(--content-font-size);
  line-height: var(--content-line-height);
}

/* 背景主题 */
body.bg-default {
  background-color: #ffffff;
  color: #333333;
}

body.bg-warm {
  background-color: #fdf6e3;
  color: #5c4317;
}

body.bg-dark {
  background-color: #2b2b2b;
  color: #e0e0e0;
}

body.bg-dark #content {
  color: #e0e0e0;
}

body.bg-dark .page-header {
  color: #4caf50;
  border-bottom-color: #4caf50;
}

body.bg-dark .header-title {
  color: #4caf50;
}

body.bg-green {
  background-color: #f0f8f0;
  color: #2d5016;
}

.settings-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.settings-content {
  background: white;
  border-radius: 12px;
  padding: 20px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* 移动端优化 */
@media (max-width: 768px) {
  .settings-content {
    width: 92%;
    max-width: none;
    margin: 5px;
    padding: 12px;
    max-height: 85vh;
    border-radius: 6px;
  }

  .settings-header {
    margin-bottom: 15px;
    padding-bottom: 8px;
  }

  .settings-header h3 {
    font-size: 1em;
  }

  .settings-close {
    width: 30px;
    height: 30px;
    font-size: 18px;
  }
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.settings-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.2em;
}

.settings-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.settings-close:hover {
  background-color: #f0f0f0;
}

.settings-section {
  margin-bottom: 25px;
}

.settings-section h4 {
  margin: 0 0 15px 0;
  color: #444;
  font-size: 1em;
  font-weight: 600;
}

/* 移动端section优化 */
@media (max-width: 768px) {
  .settings-section {
    margin-bottom: 18px;
  }

  .settings-section h4 {
    font-size: 0.9em;
    margin-bottom: 8px;
    padding-bottom: 5px;
    border-bottom: 1px solid #f0f0f0;
  }

  /* 在移动端隐藏页面设置部分 */
  .settings-section.page-settings {
    display: none;
  }
}

/* 颜色选项 */
.color-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.color-option {
  padding: 15px;
  border-radius: 8px;
  cursor: pointer;
  text-align: center;
  font-size: 0.9em;
  transition: all 0.2s;
  position: relative;
}

/* 移动端颜色选项优化 */
@media (max-width: 768px) {
  .color-options {
    grid-template-columns: repeat(4, 1fr);
    gap: 4px;
  }

  .color-option {
    padding: 6px 3px;
    font-size: 0.7em;
    min-height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .color-option.selected::after {
    font-size: 12px;
    top: 2px;
    right: 3px;
  }
}

.color-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.color-option.selected {
  border-color: #006400 !important;
  border-width: 3px !important;
}

.color-option.selected::after {
  content: "✓";
  position: absolute;
  top: 5px;
  right: 8px;
  color: #006400;
  font-weight: bold;
  font-size: 16px;
}

/* 字体设置 */
.font-options,
.font-family-options,
.line-height-options,
.width-options {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.font-options label,
.font-family-options label,
.line-height-options label,
.width-options label {
  min-width: 80px;
  font-size: 0.9em;
  color: #555;
}

.font-options input[type="range"],
.line-height-options input[type="range"],
.width-options input[type="range"] {
  flex: 1;
  margin: 0 10px;
}

.font-family-options select {
  flex: 1;
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9em;
}

/* 移动端字体设置优化 */
@media (max-width: 768px) {
  .font-options,
  .font-family-options,
  .line-height-options,
  .width-options {
    flex-direction: column;
    gap: 5px;
    margin-bottom: 12px;
  }

  .font-options label,
  .font-family-options label,
  .line-height-options label,
  .width-options label {
    min-width: auto;
    font-size: 0.8em;
    font-weight: 600;
    margin-bottom: 3px;
  }

  .font-options input[type="range"],
  .line-height-options input[type="range"],
  .width-options input[type="range"] {
    margin: 3px 0;
    width: 100%;
    height: 30px;
  }

  .font-family-options select {
    padding: 8px;
    font-size: 0.8em;
    border-radius: 4px;
  }

  #font-size-value,
  #line-height-value,
  #content-width-value {
    font-size: 0.8em;
    text-align: center;
    background: #f5f5f5;
    padding: 3px 8px;
    border-radius: 3px;
    margin-top: 3px;
  }
}

#font-size-value,
#line-height-value,
#content-width-value {
  min-width: 50px;
  font-size: 0.9em;
  color: #666;
  font-weight: 500;
}

/* 设置按钮 */
.settings-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.reset-btn,
.close-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9em;
  transition: background-color 0.2s;
}

/* 移动端按钮优化 */
@media (max-width: 768px) {
  .settings-actions {
    flex-direction: row;
    gap: 8px;
    margin-top: 15px;
    padding-top: 10px;
  }

  .reset-btn,
  .close-btn {
    padding: 10px 16px;
    font-size: 0.85em;
    border-radius: 6px;
    flex: 1;
    font-weight: 600;
  }
}

.reset-btn {
  background: #f44336;
  color: white;
}

.reset-btn:hover {
  background: #d32f2f;
}

.close-btn {
  background: #006400;
  color: white;
}

.close-btn:hover {
  background: #004d00;
}

/* 阅读设置按钮样式 */
.reading-settings {
  font-size: 16px;
  text-align: center;
  position: fixed;
  width: 40px;
  height: 40px;
  bottom: 140px;
  right: 5px;
  background-color: rgba(66, 100, 66, 0.5);
  color: white;
  border: none;
  cursor: pointer;
  border-radius: 50%;
  box-shadow: 2px 2px 5px rgba(45, 58, 49, 0.3);
  -webkit-tap-highlight-color: transparent;
  z-index: 995;
  transition: all 0.3s ease;
}

.reading-settings:hover {
  background-color: rgba(0, 100, 0, 0.5);
  transform: scale(1.1);
}
