import os
import re

def extract_first_heading(md_file_path):
    """从 Markdown 文件中提取第一个 # 一级标题的内容"""
    try:
        with open(md_file_path, "r", encoding="utf-8") as f:
            for line in f:
                if line.startswith("# "):
                    return line[2:].strip()
    except Exception as e:
        print(f"⚠️ 读取文件失败：{e}")
    return None  # 没有找到一级标题则返回 None

def natural_sort_key(filename):
    """提取文件名中的数字用于自然排序"""
    base_name = os.path.splitext(filename)[0]  # 去掉扩展名
    # 将字符串拆分为数字和非数字部分，例如 "10_test" → ['10', '_test']
    return [int(text) if text.isdigit() else text.lower() for text in re.split(r'(\d+)', base_name)]

# 获取当前目录
current_dir = os.getcwd()

# 获取目录下所有 .md 文件，并排除 toc.md 自己
md_files = [f for f in os.listdir(current_dir)
            if f.endswith(".md") and f != "toc.md"]

# 按文件名自然排序
md_files.sort(key=natural_sort_key)

# 构建目录结构
toc_content = ["目录"]

for md_file in md_files:
    file_path = os.path.join(current_dir, md_file)
    heading = extract_first_heading(file_path)

    # 如果没有找到 # 一级标题，默认使用文件名（去除 .md）
    display_name = heading if heading else os.path.splitext(md_file)[0]

    toc_content.append(f"- [{display_name}]({md_file})")

# 写入 toc.md
with open("toc.md", "w", encoding="utf-8") as f:
    f.write("\n".join(toc_content))

print("✅ 已生成 toc.md，内容按照文件名自然排序。")
